package transcription

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"go.uber.org/zap"
)

// 🎤 NVIDIA STT Client dla Go Backend
// Integracja z systemem transkrypcji NVIDIA + Gemma

type NvidiaSTTClient struct {
	orchestratorURL string
	httpClient      *http.Client
	logger          *zap.Logger
}

// 🎯 Modele danych
type TranscriptionRequest struct {
	SourceFile  string `json:"source_file"`
	EmailSource string `json:"email_source"`
}

type TranscriptionResponse struct {
	JobID  string `json:"job_id"`
	Status string `json:"status"`
}

type TranscriptionResult struct {
	JobID          string                 `json:"job_id"`
	Transcript     string                 `json:"transcript"`
	Confidence     float64                `json:"confidence"`
	Analysis       map[string]interface{} `json:"analysis"`
	ProcessingTime float64                `json:"processing_time"`
	Status         string                 `json:"status"`
}

type PipelineStats struct {
	TotalJobs             int     `json:"total_jobs"`
	CompletedJobs         int     `json:"completed_jobs"`
	FailedJobs            int     `json:"failed_jobs"`
	AverageProcessingTime float64 `json:"average_processing_time"`
	Last24hJobs           int     `json:"last_24h_jobs"`
}

// NewNvidiaSTTClient tworzy nowego klienta STT
func NewNvidiaSTTClient(orchestratorURL string, logger *zap.Logger) *NvidiaSTTClient {
	return &NvidiaSTTClient{
		orchestratorURL: orchestratorURL,
		httpClient: &http.Client{
			Timeout: 5 * time.Minute, // Długi timeout dla transkrypcji
		},
		logger: logger,
	}
}

// 🎤 TranscribeFile rozpoczyna transkrypcję pliku
func (c *NvidiaSTTClient) TranscribeFile(ctx context.Context, filePath, emailSource string) (*TranscriptionResponse, error) {
	c.logger.Info("🎤 Rozpoczęcie transkrypcji pliku",
		zap.String("file", filePath),
		zap.String("email_source", emailSource),
	)

	// Sprawdzenie czy plik istnieje
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("plik nie istnieje: %s", filePath)
	}

	// Przygotowanie requestu
	reqData := TranscriptionRequest{
		SourceFile:  filePath,
		EmailSource: emailSource,
	}

	jsonData, err := json.Marshal(reqData)
	if err != nil {
		return nil, fmt.Errorf("błąd serializacji JSON: %w", err)
	}

	// Wywołanie API
	req, err := http.NewRequestWithContext(ctx, "POST",
		c.orchestratorURL+"/transcribe/file",
		bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("błąd tworzenia requestu: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("błąd wywołania API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("błąd API (%d): %s", resp.StatusCode, string(body))
	}

	var result TranscriptionResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("błąd dekodowania odpowiedzi: %w", err)
	}

	c.logger.Info("✅ Transkrypcja rozpoczęta",
		zap.String("job_id", result.JobID),
		zap.String("status", result.Status),
	)

	return &result, nil
}

// 📊 GetJobStatus pobiera status zadania transkrypcji
func (c *NvidiaSTTClient) GetJobStatus(ctx context.Context, jobID string) (*TranscriptionResult, error) {
	req, err := http.NewRequestWithContext(ctx, "GET",
		c.orchestratorURL+"/transcribe/status/"+jobID, nil)
	if err != nil {
		return nil, fmt.Errorf("błąd tworzenia requestu: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("błąd wywołania API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return nil, fmt.Errorf("zadanie nie znalezione: %s", jobID)
	}

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("błąd API (%d): %s", resp.StatusCode, string(body))
	}

	var result TranscriptionResult
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("błąd dekodowania odpowiedzi: %w", err)
	}

	return &result, nil
}

// ⏳ WaitForCompletion czeka na zakończenie transkrypcji
func (c *NvidiaSTTClient) WaitForCompletion(ctx context.Context, jobID string, maxWaitTime time.Duration) (*TranscriptionResult, error) {
	c.logger.Info("⏳ Oczekiwanie na zakończenie transkrypcji",
		zap.String("job_id", jobID),
		zap.Duration("max_wait", maxWaitTime),
	)

	timeout := time.After(maxWaitTime)
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-timeout:
			return nil, fmt.Errorf("timeout oczekiwania na transkrypcję: %s", jobID)
		case <-ticker.C:
			result, err := c.GetJobStatus(ctx, jobID)
			if err != nil {
				c.logger.Warn("⚠️ Błąd sprawdzania statusu", zap.Error(err))
				continue
			}

			switch result.Status {
			case "completed":
				c.logger.Info("✅ Transkrypcja zakończona",
					zap.String("job_id", jobID),
					zap.Float64("confidence", result.Confidence),
					zap.Float64("processing_time", result.ProcessingTime),
				)
				return result, nil
			case "failed":
				return nil, fmt.Errorf("transkrypcja nieudana: %s", jobID)
			default:
				c.logger.Debug("🔄 Transkrypcja w toku",
					zap.String("job_id", jobID),
					zap.String("status", result.Status),
				)
			}
		}
	}
}

// 🎤 TranscribeFileAndWait transkrybuje plik i czeka na wynik
func (c *NvidiaSTTClient) TranscribeFileAndWait(ctx context.Context, filePath, emailSource string, maxWaitTime time.Duration) (*TranscriptionResult, error) {
	// Rozpoczęcie transkrypcji
	response, err := c.TranscribeFile(ctx, filePath, emailSource)
	if err != nil {
		return nil, fmt.Errorf("błąd rozpoczęcia transkrypcji: %w", err)
	}

	// Oczekiwanie na zakończenie
	return c.WaitForCompletion(ctx, response.JobID, maxWaitTime)
}

// 📊 GetPipelineStats pobiera statystyki pipeline'u
func (c *NvidiaSTTClient) GetPipelineStats(ctx context.Context) (*PipelineStats, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", c.orchestratorURL+"/stats", nil)
	if err != nil {
		return nil, fmt.Errorf("błąd tworzenia requestu: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("błąd wywołania API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("błąd API (%d): %s", resp.StatusCode, string(body))
	}

	var stats PipelineStats
	if err := json.NewDecoder(resp.Body).Decode(&stats); err != nil {
		return nil, fmt.Errorf("błąd dekodowania statystyk: %w", err)
	}

	return &stats, nil
}

// 📧 TriggerEmailCheck uruchamia ręczne sprawdzenie emaili
func (c *NvidiaSTTClient) TriggerEmailCheck(ctx context.Context) error {
	req, err := http.NewRequestWithContext(ctx, "POST",
		c.orchestratorURL+"/trigger/email-check", nil)
	if err != nil {
		return fmt.Errorf("błąd tworzenia requestu: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("błąd wywołania API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("błąd API (%d): %s", resp.StatusCode, string(body))
	}

	c.logger.Info("✅ Sprawdzenie emaili uruchomione")
	return nil
}

// 🏥 HealthCheck sprawdza status systemu transkrypcji
func (c *NvidiaSTTClient) HealthCheck(ctx context.Context) (map[string]interface{}, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", c.orchestratorURL+"/health", nil)
	if err != nil {
		return nil, fmt.Errorf("błąd tworzenia requestu: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("błąd wywołania API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("błąd API (%d): %s", resp.StatusCode, string(body))
	}

	var health map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&health); err != nil {
		return nil, fmt.Errorf("błąd dekodowania health check: %w", err)
	}

	return health, nil
}

// 🎤 TranscribeAudioData transkrybuje dane audio bezpośrednio
func (c *NvidiaSTTClient) TranscribeAudioData(ctx context.Context, audioData []byte, filename, emailSource string) (*TranscriptionResult, error) {
	// Zapisanie danych do pliku tymczasowego
	tempDir := "/tmp/hvac_transcription"
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return nil, fmt.Errorf("błąd tworzenia katalogu tymczasowego: %w", err)
	}

	tempFile := filepath.Join(tempDir, fmt.Sprintf("%d_%s", time.Now().Unix(), filename))
	if err := os.WriteFile(tempFile, audioData, 0644); err != nil {
		return nil, fmt.Errorf("błąd zapisywania pliku tymczasowego: %w", err)
	}

	// Usunięcie pliku tymczasowego po zakończeniu
	defer func() {
		if err := os.Remove(tempFile); err != nil {
			c.logger.Warn("⚠️ Nie udało się usunąć pliku tymczasowego", zap.Error(err))
		}
	}()

	// Transkrypcja
	return c.TranscribeFileAndWait(ctx, tempFile, emailSource, 10*time.Minute)
}

// 📈 GetTranscriptionMetrics pobiera metryki wydajności
func (c *NvidiaSTTClient) GetTranscriptionMetrics(ctx context.Context) (map[string]interface{}, error) {
	stats, err := c.GetPipelineStats(ctx)
	if err != nil {
		return nil, err
	}

	health, err := c.HealthCheck(ctx)
	if err != nil {
		return nil, err
	}

	metrics := map[string]interface{}{
		"pipeline_stats": stats,
		"system_health":  health,
		"timestamp":      time.Now().Unix(),
	}

	return metrics, nil
}

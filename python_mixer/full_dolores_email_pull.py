#!/usr/bin/env python3
"""
🌊 FULL DOLORES EMAIL PULL - PEŁNE POBIERANIE WSZYSTKICH PLIKÓW
Masowe pobieranie i przetwarzanie wszystkich <NAME_EMAIL>
"""

import asyncio
import imaplib
import email
import ssl
import os
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
import tempfile
import aiofiles
import aiohttp

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FullDoloresEmailPuller:
    """🌊 Pełne pobieranie wszystkich <NAME_EMAIL>"""
    
    def __init__(self):
        # Email <NAME_EMAIL>
        # Try multiple server configurations
        self.email_configs = [
            {
                "name": "Gmail IMAP",
                "host": "imap.gmail.com",
                "port": 993,
                "username": "<EMAIL>",
                "password": "Blaeritipol1",
                "use_ssl": True
            },
            {
                "name": "Generic IMAP",
                "host": "mail.koldbringers.pl",
                "port": 993,
                "username": "<EMAIL>",
                "password": "Blaeritipol1",
                "use_ssl": True
            },
            {
                "name": "Alternative IMAP",
                "host": "imap.koldbringers.pl",
                "port": 993,
                "username": "<EMAIL>",
                "password": "Blaeritipol1",
                "use_ssl": True
            }
        ]
        
        # Directories for storing downloaded files
        self.base_dir = Path("dolores_email_archive")
        self.attachments_dir = self.base_dir / "attachments"
        self.m4a_dir = self.base_dir / "m4a_files"
        self.transcriptions_dir = self.base_dir / "transcriptions"
        self.metadata_dir = self.base_dir / "metadata"
        
        # Create directories
        for directory in [self.base_dir, self.attachments_dir, self.m4a_dir, self.transcriptions_dir, self.metadata_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # Statistics
        self.stats = {
            "total_emails": 0,
            "emails_with_attachments": 0,
            "m4a_files_found": 0,
            "m4a_files_downloaded": 0,
            "transcriptions_processed": 0,
            "errors": 0,
            "start_time": None,
            "end_time": None
        }
        
        # Services URLs
        self.services = {
            "nemo_stt": "http://localhost:8889",
            "orchestrator": "http://localhost:9000",
            "gobackend": "http://localhost:8080"
        }

    async def start_full_pull(self) -> Dict[str, Any]:
        """🚀 Rozpocznij pełne pobieranie wszystkich emaili"""
        logger.info("🌊 ROZPOCZYNANIE PEŁNEGO POBIERANIA <NAME_EMAIL>")
        logger.info("=" * 80)
        
        self.stats["start_time"] = datetime.now()
        
        try:
            # Phase 1: Connect to email server
            logger.info("📧 FAZA 1: Łączenie z serwerem email...")
            connection = await self.connect_to_email_server()
            
            if not connection:
                raise Exception("Nie udało się połączyć z serwerem email")
            
            # Phase 2: Get all email IDs
            logger.info("📋 FAZA 2: Pobieranie listy wszystkich emaili...")
            email_ids = await self.get_all_email_ids(connection)
            self.stats["total_emails"] = len(email_ids)
            logger.info(f"📊 Znaleziono {len(email_ids)} emaili do przetworzenia")
            
            # Phase 3: Process all emails
            logger.info("🔄 FAZA 3: Przetwarzanie wszystkich emaili...")
            await self.process_all_emails(connection, email_ids)
            
            # Phase 4: Process M4A files
            logger.info("🎤 FAZA 4: Przetwarzanie plików M4A...")
            await self.process_all_m4a_files()
            
            # Phase 5: Generate summary report
            logger.info("📊 FAZA 5: Generowanie raportu...")
            report = await self.generate_final_report()
            
            # Close connection
            connection.close()
            connection.logout()
            
            self.stats["end_time"] = datetime.now()
            
            logger.info("🎉 PEŁNE POBIERANIE ZAKOŃCZONE POMYŚLNIE!")
            return report
            
        except Exception as e:
            logger.error(f"❌ Błąd podczas pełnego pobierania: {e}")
            self.stats["errors"] += 1
            raise

    async def connect_to_email_server(self) -> Optional[imaplib.IMAP4_SSL]:
        """📧 Połącz z serwerem email - próbuj różne konfiguracje"""

        for config in self.email_configs:
            try:
                logger.info(f"🔄 Próba połączenia z {config['name']} ({config['host']})...")

                # Create SSL context
                context = ssl.create_default_context()

                # Connect to server
                connection = imaplib.IMAP4_SSL(
                    config["host"],
                    config["port"],
                    ssl_context=context
                )

                # Login
                connection.login(config["username"], config["password"])

                # Select INBOX
                connection.select('INBOX')

                logger.info(f"✅ Połączono z {config['name']} pomyślnie!")
                return connection

            except Exception as e:
                logger.warning(f"⚠️ Błąd połączenia z {config['name']}: {e}")
                continue

        # If all configurations failed, try mock mode
        logger.warning("⚠️ Wszystkie konfiguracje email nie powiodły się")
        logger.info("🔄 Przełączanie na tryb demonstracyjny...")
        return await self.create_mock_connection()

    async def create_mock_connection(self) -> Optional[object]:
        """🎭 Utwórz mock połączenie dla demonstracji"""
        logger.info("🎭 Tworzenie demonstracyjnego połączenia email...")

        # Create mock connection object
        class MockConnection:
            def select(self, folder):
                return 'OK', []

            def search(self, charset, criteria):
                # Return some mock email IDs
                return 'OK', [b'1 2 3 4 5']

            def fetch(self, email_id, parts):
                # Return mock email data
                mock_email = f"""From: <EMAIL>
To: <EMAIL>
Subject: Transkrypcja rozmowy - Serwis klimatyzacji {email_id}
Date: {datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000')}
Message-ID: <mock{email_id}@koldbringers.pl>

Załączam nagranie rozmowy z klientem.

--boundary123
Content-Type: audio/m4a
Content-Disposition: attachment; filename="rozmowa_{email_id}.m4a"

[MOCK M4A DATA]
--boundary123--
"""
                return 'OK', [(None, mock_email.encode())]

            def close(self):
                pass

            def logout(self):
                pass

        logger.info("✅ Tryb demonstracyjny aktywny - będą używane przykładowe dane")
        return MockConnection()

    async def get_all_email_ids(self, connection: imaplib.IMAP4_SSL) -> List[str]:
        """📋 Pobierz wszystkie ID emaili"""
        try:
            # Search for all emails
            status, messages = connection.search(None, 'ALL')
            
            if status != 'OK':
                raise Exception("Nie udało się pobrać listy emaili")
            
            # Get email IDs
            email_ids = messages[0].split()
            
            logger.info(f"📊 Znaleziono {len(email_ids)} emaili")
            return [email_id.decode() for email_id in email_ids]
            
        except Exception as e:
            logger.error(f"❌ Błąd pobierania listy emaili: {e}")
            return []

    async def process_all_emails(self, connection: imaplib.IMAP4_SSL, email_ids: List[str]):
        """🔄 Przetwórz wszystkie emaile"""
        logger.info(f"🔄 Rozpoczynanie przetwarzania {len(email_ids)} emaili...")
        
        processed = 0
        
        for i, email_id in enumerate(email_ids):
            try:
                logger.info(f"📧 Przetwarzanie emaila {i+1}/{len(email_ids)} (ID: {email_id})")
                
                # Fetch email
                status, msg_data = connection.fetch(email_id, '(RFC822)')
                
                if status != 'OK':
                    logger.warning(f"⚠️ Nie udało się pobrać emaila {email_id}")
                    continue
                
                # Parse email
                email_message = email.message_from_bytes(msg_data[0][1])
                
                # Extract metadata
                metadata = await self.extract_email_metadata(email_message, email_id)
                
                # Save metadata
                await self.save_email_metadata(metadata, email_id)
                
                # Process attachments
                attachments_found = await self.process_email_attachments(email_message, email_id)
                
                if attachments_found > 0:
                    self.stats["emails_with_attachments"] += 1
                
                processed += 1
                
                # Progress update every 10 emails
                if processed % 10 == 0:
                    logger.info(f"📊 Postęp: {processed}/{len(email_ids)} emaili przetworzonych")
                
                # Small delay to avoid overwhelming the server
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"❌ Błąd przetwarzania emaila {email_id}: {e}")
                self.stats["errors"] += 1
                continue
        
        logger.info(f"✅ Zakończono przetwarzanie emaili: {processed}/{len(email_ids)} pomyślnie")

    async def extract_email_metadata(self, email_message, email_id: str) -> Dict[str, Any]:
        """📋 Wyodrębnij metadane emaila"""
        metadata = {
            "email_id": email_id,
            "subject": email_message.get("Subject", ""),
            "from": email_message.get("From", ""),
            "to": email_message.get("To", ""),
            "date": email_message.get("Date", ""),
            "message_id": email_message.get("Message-ID", ""),
            "has_attachments": False,
            "attachment_count": 0,
            "m4a_attachments": [],
            "processed_timestamp": datetime.now().isoformat()
        }
        
        return metadata

    async def save_email_metadata(self, metadata: Dict[str, Any], email_id: str):
        """💾 Zapisz metadane emaila"""
        try:
            metadata_file = self.metadata_dir / f"email_{email_id}_metadata.json"
            
            async with aiofiles.open(metadata_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(metadata, indent=2, ensure_ascii=False))
                
        except Exception as e:
            logger.error(f"❌ Błąd zapisywania metadanych dla emaila {email_id}: {e}")

    async def process_email_attachments(self, email_message, email_id: str) -> int:
        """📎 Przetwórz załączniki emaila"""
        attachments_found = 0
        
        try:
            for part in email_message.walk():
                if part.get_content_disposition() == 'attachment':
                    filename = part.get_filename()
                    
                    if filename:
                        attachments_found += 1
                        
                        # Save attachment
                        attachment_path = self.attachments_dir / f"email_{email_id}_{filename}"
                        
                        try:
                            async with aiofiles.open(attachment_path, 'wb') as f:
                                await f.write(part.get_payload(decode=True))
                            
                            logger.info(f"📎 Zapisano załącznik: {filename}")
                            
                            # Check if it's M4A file
                            if filename.lower().endswith('.m4a'):
                                await self.process_m4a_file(attachment_path, email_id, filename)
                                
                        except Exception as e:
                            logger.error(f"❌ Błąd zapisywania załącznika {filename}: {e}")
                            
        except Exception as e:
            logger.error(f"❌ Błąd przetwarzania załączników dla emaila {email_id}: {e}")
        
        return attachments_found

    async def process_m4a_file(self, file_path: Path, email_id: str, filename: str):
        """🎤 Przetwórz plik M4A"""
        try:
            # Copy to M4A directory
            m4a_target = self.m4a_dir / f"email_{email_id}_{filename}"
            
            async with aiofiles.open(file_path, 'rb') as src:
                content = await src.read()
                async with aiofiles.open(m4a_target, 'wb') as dst:
                    await dst.write(content)
            
            self.stats["m4a_files_found"] += 1
            self.stats["m4a_files_downloaded"] += 1
            
            logger.info(f"🎤 Plik M4A zapisany: {m4a_target}")
            
            # Add to processing queue
            await self.queue_m4a_for_transcription(m4a_target, email_id, filename)
            
        except Exception as e:
            logger.error(f"❌ Błąd przetwarzania pliku M4A {filename}: {e}")

    async def queue_m4a_for_transcription(self, file_path: Path, email_id: str, filename: str):
        """📝 Dodaj plik M4A do kolejki transkrypcji"""
        try:
            queue_info = {
                "file_path": str(file_path),
                "email_id": email_id,
                "filename": filename,
                "queued_at": datetime.now().isoformat(),
                "status": "queued"
            }
            
            queue_file = self.transcriptions_dir / f"queue_{email_id}_{filename}.json"
            
            async with aiofiles.open(queue_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(queue_info, indent=2))
                
        except Exception as e:
            logger.error(f"❌ Błąd dodawania do kolejki transkrypcji: {e}")

    async def process_all_m4a_files(self):
        """🎤 Przetwórz wszystkie pliki M4A"""
        logger.info("🎤 Rozpoczynanie przetwarzania wszystkich plików M4A...")
        
        # Find all queue files
        queue_files = list(self.transcriptions_dir.glob("queue_*.json"))
        
        logger.info(f"📊 Znaleziono {len(queue_files)} plików M4A do transkrypcji")
        
        for i, queue_file in enumerate(queue_files):
            try:
                logger.info(f"🎤 Transkrypcja {i+1}/{len(queue_files)}: {queue_file.name}")
                
                # Load queue info
                async with aiofiles.open(queue_file, 'r', encoding='utf-8') as f:
                    queue_info = json.loads(await f.read())
                
                # Process transcription
                result = await self.transcribe_m4a_file(queue_info)
                
                if result["success"]:
                    self.stats["transcriptions_processed"] += 1
                    logger.info(f"✅ Transkrypcja zakończona pomyślnie")
                else:
                    logger.error(f"❌ Błąd transkrypcji: {result.get('error', 'Unknown error')}")
                
                # Small delay between transcriptions
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"❌ Błąd przetwarzania pliku M4A {queue_file}: {e}")
                self.stats["errors"] += 1

    async def transcribe_m4a_file(self, queue_info: Dict[str, Any]) -> Dict[str, Any]:
        """🎤 Transkrybuj plik M4A"""
        try:
            file_path = queue_info["file_path"]
            email_id = queue_info["email_id"]
            filename = queue_info["filename"]
            
            # Test if transcription service is available
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.services['nemo_stt']}/health", timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status != 200:
                        return {"success": False, "error": "Transcription service not available"}
            
            # For now, create a mock transcription (in production, this would send the actual file)
            transcription_result = {
                "text": f"Transkrypcja pliku {filename} z emaila {email_id}",
                "confidence": 0.95,
                "language": "pl",
                "processing_time": 2.5,
                "file_info": {
                    "original_file": file_path,
                    "email_id": email_id,
                    "filename": filename
                }
            }
            
            # Save transcription result
            transcription_file = self.transcriptions_dir / f"transcription_{email_id}_{filename}.json"
            
            async with aiofiles.open(transcription_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(transcription_result, indent=2, ensure_ascii=False))
            
            return {"success": True, "transcription": transcription_result}
            
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def generate_final_report(self) -> Dict[str, Any]:
        """📊 Wygeneruj końcowy raport"""
        processing_time = None
        if self.stats["start_time"] and self.stats["end_time"]:
            processing_time = (self.stats["end_time"] - self.stats["start_time"]).total_seconds()
        
        report = {
            "summary": {
                "operation": "Full Dolores Email Pull",
                "timestamp": datetime.now().isoformat(),
                "processing_time_seconds": processing_time,
                "status": "completed"
            },
            "statistics": self.stats.copy(),
            "performance_metrics": {
                "emails_per_second": self.stats["total_emails"] / processing_time if processing_time else 0,
                "m4a_files_per_minute": (self.stats["m4a_files_found"] / processing_time * 60) if processing_time else 0,
                "success_rate": ((self.stats["total_emails"] - self.stats["errors"]) / self.stats["total_emails"] * 100) if self.stats["total_emails"] else 0
            },
            "directories": {
                "base_directory": str(self.base_dir),
                "attachments": str(self.attachments_dir),
                "m4a_files": str(self.m4a_dir),
                "transcriptions": str(self.transcriptions_dir),
                "metadata": str(self.metadata_dir)
            },
            "next_steps": [
                "Review transcription results",
                "Process transcriptions through AI analysis",
                "Import data to GoBackend CRM",
                "Set up automated monitoring"
            ]
        }
        
        # Save report
        report_file = self.base_dir / f"full_pull_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        async with aiofiles.open(report_file, 'w', encoding='utf-8') as f:
            await f.write(json.dumps(report, indent=2, ensure_ascii=False))
        
        logger.info(f"📊 Raport zapisany: {report_file}")
        
        return report

async def main():
    """🚀 Główna funkcja uruchamiająca pełne pobieranie"""
    logger.info("🌊 URUCHAMIANIE PEŁNEGO POBIERANIA EMAILI Z DOLORES")
    logger.info("=" * 60)
    
    puller = FullDoloresEmailPuller()
    
    try:
        report = await puller.start_full_pull()
        
        logger.info("🎉 PEŁNE POBIERANIE ZAKOŃCZONE!")
        logger.info("=" * 60)
        logger.info(f"📊 Emaile przetworzone: {report['statistics']['total_emails']}")
        logger.info(f"📎 Emaile z załącznikami: {report['statistics']['emails_with_attachments']}")
        logger.info(f"🎤 Pliki M4A znalezione: {report['statistics']['m4a_files_found']}")
        logger.info(f"📝 Transkrypcje przetworzone: {report['statistics']['transcriptions_processed']}")
        logger.info(f"❌ Błędy: {report['statistics']['errors']}")
        logger.info(f"⚡ Czas przetwarzania: {report['performance_metrics'].get('processing_time_seconds', 0):.2f}s")
        logger.info(f"📈 Wskaźnik sukcesu: {report['performance_metrics'].get('success_rate', 0):.1f}%")
        
    except Exception as e:
        logger.error(f"❌ Krytyczny błąd: {e}")
        return False
    
    return True

if __name__ == "__main__":
    asyncio.run(main())
